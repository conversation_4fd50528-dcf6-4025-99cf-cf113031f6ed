'use client';

import { motion, useScroll, useTransform } from 'framer-motion';
import Image from 'next/image';
import { useRef, useEffect, useState } from 'react';
import TeamMembers from '@/components/TeamMembers';
import CompanyStory from '@/components/CompanyStory';
import Link from 'next/link';

const values = [
  {
    title: 'Innovation',
    icon: 'far fa-lightbulb',
    description: 'Pushing boundaries with creative digital solutions',
    color: 'from-[#FF5400] to-[#ff7633]'
  },
  {
    title: 'Quality',
    icon: 'far fa-circle-check',
    description: 'Delivering excellence in every project we undertake',
    color: 'from-[#0A2647] to-[#205295]'
  },
  {
    title: 'Integrity',
    icon: 'far fa-heart',
    description: 'Building trust through honest business practices',
    color: 'from-[#FF5400] to-[#ff7633]'
  },
  {
    title: 'Collaboration',
    icon: 'far fa-handshake',
    description: 'Working together to achieve exceptional results',
    color: 'from-[#0A2647] to-[#205295]'
  }
];

// Custom animated counter component
function Counter({ from, to, duration = 2 }) {
  const [count, setCount] = useState(from);

  useEffect(() => {
    let startTime;
    let animationFrame;

    const updateCount = (timestamp) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / (duration * 1000), 1);
      setCount(Math.floor(progress * (to - from) + from));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(updateCount);
      }
    };

    animationFrame = requestAnimationFrame(updateCount);
    return () => cancelAnimationFrame(animationFrame);
  }, [from, to, duration]);

  return <span>{count}+</span>;
}

export default function About() {
  const targetRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: targetRef,
    offset: ["start end", "end start"]
  });

  const opacity = useTransform(scrollYProgress, [0, 0.5], [0, 1]);
  const y = useTransform(scrollYProgress, [0, 0.5], [50, 0]);

  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (targetRef.current) {
      observer.observe(targetRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <main className="pt-24 overflow-hidden">
      {/* Hero Section - Advanced 3D Design */}
      <section className="relative min-h-[70vh] flex items-center justify-evenly pt-10 md:pt-16">
        {/* 3D Background Elements */}
        <div className="absolute inset-0 -z-10 bg-white overflow-hidden">
          <div className="absolute w-full h-full bg-[url('/images/grid.svg')] opacity-[0.1]"></div>

          {/* Animated Gradient Orbs */}
          <div className="absolute top-1/4 -right-20 w-80 h-80 bg-[#0A2647] rounded-full mix-blend-multiply filter blur-[80px] opacity-20 animate-float-slow"></div>
          <div className="absolute top-3/4 -left-20 w-80 h-80 bg-[#FF5400] rounded-full mix-blend-multiply filter blur-[80px] opacity-15 animate-float-slow-reverse"></div>
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-[#0A2647]/10 rounded-full mix-blend-multiply filter blur-[80px] opacity-10 animate-pulse-slow"></div>

          {/* Additional Visual Elements */}
          <div className="absolute top-[15%] left-[10%] w-60 h-60 bg-[#FF7633] rounded-full mix-blend-multiply filter blur-[60px] opacity-10 animate-float-slow"></div>
          <div className="absolute bottom-[15%] right-[10%] w-60 h-60 bg-[#0A2647] rounded-full mix-blend-multiply filter blur-[60px] opacity-10 animate-float-slow-reverse"></div>



          {/* Subtle Particles */}
          <div className="absolute top-[10%] left-[20%] w-1 h-1 rounded-full bg-gray-400 opacity-50"></div>
          <div className="absolute top-[30%] left-[80%] w-1 h-1 rounded-full bg-gray-400 opacity-50"></div>
          <div className="absolute top-[70%] left-[40%] w-1 h-1 rounded-full bg-gray-400 opacity-50"></div>
          <div className="absolute top-[50%] left-[10%] w-1 h-1 rounded-full bg-gray-400 opacity-50"></div>
          <div className="absolute top-[20%] left-[60%] w-1 h-1 rounded-full bg-gray-400 opacity-50"></div>
          <div className="absolute top-[80%] left-[70%] w-1 h-1 rounded-full bg-gray-400 opacity-50"></div>
          <div className="absolute top-[40%] left-[30%] w-1 h-1 rounded-full bg-gray-400 opacity-50"></div>
          <div className="absolute top-[60%] left-[90%] w-1 h-1 rounded-full bg-gray-400 opacity-50"></div>
        </div>

        <div className="container relative z-10">
          <div className="max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-center max-w-4xl mx-auto pt-6 sm:pt-10"
            >
              <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold mb-6 sm:mb-8 text-gray-900 leading-tight px-4 sm:px-0 pt-4 sm:pt-0">
                We Create <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#FF5400] to-[#ff7633]">Digital</span> Experiences
              </h1>

              <p className="text-lg sm:text-xl text-gray-600 leading-relaxed mb-8 sm:mb-10 max-w-3xl mx-auto px-4">
                A creative digital agency focused on growing brands through innovative design and development solutions that transform businesses in the digital landscape.
              </p>

              <div className="flex flex-wrap gap-4 sm:gap-6 justify-center px-4">
                <Link href="/contact" className="px-6 sm:px-8 py-3 rounded-full bg-[#FF5400] text-white font-medium hover:bg-[#cc4300] hover:shadow-lg hover:shadow-[#FF5400]/20 transition-all duration-300 transform hover:-translate-y-1">
                  Get in Touch
                </Link>
                <Link href="/portfolio" className="px-6 sm:px-8 py-3 rounded-full bg-gray-100 border border-gray-200 text-gray-700 font-medium hover:bg-gray-200 transition-all duration-300">
                  View Our Work
                </Link>
              </div>

              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 1, delay: 0.6 }}
                className="relative mt-16"
              >
                <div className="absolute -top-10 left-1/2 -translate-x-1/2 w-40 h-40 bg-[#FF5400] rounded-full mix-blend-multiply filter blur-[80px] opacity-20 animate-pulse-slow"></div>
                <div className="absolute -bottom-10 left-1/2 -translate-x-1/2 w-40 h-40 bg-[#0A2647] rounded-full mix-blend-multiply filter blur-[80px] opacity-20 animate-pulse-slower"></div>

                <div className="relative z-10 py-6 px-6 sm:px-8 rounded-2xl bg-gray-50 border border-gray-200">
                  <div className="text-xl sm:text-2xl text-gray-500 font-serif text-center">" Transforming ideas into digital reality "</div>
                </div>
              </motion.div>
            </motion.div>


          </div>
        </div>


      </section>

      {/* Our Story Section */}
      <CompanyStory />

      {/* Values Section - Glassmorphism Design */}
      <section className="py-24 relative overflow-hidden bg-gradient-to-b from-[#0A1929] to-[#0A2647]">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-full bg-[url('/images/grid.svg')] opacity-[0.03]"></div>
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-[#205295] rounded-full mix-blend-multiply filter blur-[80px] opacity-20"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-[#0A2647] rounded-full mix-blend-multiply filter blur-[80px] opacity-20"></div>
        </div>

        <div className="container relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <span className="inline-block px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-md border border-white/10 text-white/80 text-xs font-medium mb-4">Our Principles</span>
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
              Core Values
            </h2>
            <div className="h-1 w-20 bg-gradient-to-r from-[#FF5400] to-[#ff7633] mx-auto mb-6" />
            <p className="text-white/70 text-lg max-w-2xl mx-auto">
              The foundation of our work ethic and the promises we make to our clients.
            </p>
          </motion.div>

          <div ref={targetRef} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            {values.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -10, transition: { duration: 0.3 } }}
                className="group relative p-8 rounded-xl bg-white/5 backdrop-blur-md border border-white/10 hover:bg-white/10 transition-all duration-300"
              >
                <div className="absolute -top-5 -left-5 w-20 h-20 bg-gradient-to-br from-[#FF5400]/20 to-[#0A2647]/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <div className="mb-6 w-14 h-14 rounded-full bg-white/10 flex items-center justify-center text-white">
                  <i className={`${value.icon} text-2xl`}></i>
                </div>

                <h3 className="text-xl font-bold text-white mb-3">
                  {value.title}
                </h3>
                <p className="text-white/70">
                  {value.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Members Section */}
      <TeamMembers />


    </main>
  );
}