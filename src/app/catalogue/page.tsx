'use client';

import { useState, useEffect } from 'react';
import SimplePricingCard from '@/components/SimplePricingCard';
import { 
  MagnifyingGlassIcon, 
  SparklesIcon,
  ChevronDownIcon,
  AdjustmentsHorizontalIcon,
  XMarkIcon,
  Bars3Icon
} from '@heroicons/react/24/outline';

interface Unit {
  id: string;
  name: string;
  displayName: string;
  plural: string;
  shortForm?: string;
  category: string;
}

interface CatalogueItem {
  id: string;
  service: string;
  price: number;
  designFee?: number;
  description?: string;
  features?: string[];
  icon?: string;
  popular?: boolean;
  imageUrl?: string;
  imageUrl2?: string;
  imageUrl3?: string;
  category?: string;
  pricingType?: string;
  unitType?: string; // Deprecated - for backward compatibility
  unitId?: string;
  unit?: Unit;
  pricePerMeter?: number;
  createdAt: string;
  updatedAt: string;
}

interface FilterState {
  categories: string[];
  priceRange: {
    min: number;
    max: number;
  };
  designFeeRange: {
    min: number;
    max: number;
  };
  popular: boolean;
  newest: boolean;
}

export default function CataloguePage() {
  const [catalogueItems, setCatalogueItems] = useState<CatalogueItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'price-low' | 'price-high' | 'newest' | 'popular'>('name');
  
  // Filter state
  const [filters, setFilters] = useState<FilterState>({
    categories: [],
    priceRange: { min: 0, max: 50000 },
    designFeeRange: { min: 0, max: 10000 },
    popular: false,
    newest: false
  });

  // Fetch catalogue items
  useEffect(() => {
    const fetchCatalogueItems = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/catalogue?t=${Date.now()}`);
        if (response.ok) {
          const data = await response.json();
          const items = Array.isArray(data) ? data : (data.items || data.data || []);
          setCatalogueItems(items);
          
          // Set initial price range based on actual data
          if (items.length > 0) {
            const prices = items.map((item: CatalogueItem) => item.price);
            const designFees = items.map((item: CatalogueItem) => item.designFee || 0).filter((fee: number) => fee > 0);
            
            setFilters(prev => ({
              ...prev,
              priceRange: {
                min: 0,
                max: Math.max(...prices)
              },
              designFeeRange: {
                min: 0,
                max: designFees.length > 0 ? Math.max(...designFees) : 10000
              }
            }));
          }
        } else {
          console.error('Failed to fetch catalogue items:', response.status, response.statusText);
        }
      } catch (error) {
        console.error('Error fetching catalogue items:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCatalogueItems();
  }, []);

  // Get unique categories
  const categories = Array.from(new Set(catalogueItems.map(item => item.category || 'Other'))).sort();

  // Filter and sort items
  const filteredAndSortedItems = catalogueItems
    .filter(item => {
      // Search filter
      const matchesSearch = !searchTerm || 
        item.service.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()));

      // Category filter
      const matchesCategory = filters.categories.length === 0 || 
        filters.categories.includes(item.category || 'Other');

      // Price filter
      const matchesPrice = item.price >= filters.priceRange.min && item.price <= filters.priceRange.max;

      // Design fee filter
      const designFee = item.designFee || 0;
      const matchesDesignFee = designFee >= filters.designFeeRange.min && designFee <= filters.designFeeRange.max;

      // Popular filter
      const matchesPopular = !filters.popular || item.popular;

      // Newest filter (items from last 30 days)
      const matchesNewest = !filters.newest || 
        new Date(item.createdAt) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

      return matchesSearch && matchesCategory && matchesPrice && matchesDesignFee && matchesPopular && matchesNewest;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return a.price - b.price;
        case 'price-high':
          return b.price - a.price;
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'popular':
          return (b.popular ? 1 : 0) - (a.popular ? 1 : 0);
        case 'name':
        default:
          return a.service.localeCompare(b.service);
      }
    });

  // Handle category filter
  const toggleCategory = (category: string) => {
    setFilters(prev => ({
      ...prev,
      categories: prev.categories.includes(category)
        ? prev.categories.filter(c => c !== category)
        : [...prev.categories, category]
    }));
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      categories: [],
      priceRange: { min: 0, max: Math.max(...catalogueItems.map(item => item.price)) },
      designFeeRange: { min: 0, max: Math.max(...catalogueItems.map(item => item.designFee || 0)) },
      popular: false,
      newest: false
    });
  };

  // Get active filter count
  const activeFilterCount = 
    filters.categories.length + 
    (filters.popular ? 1 : 0) + 
    (filters.newest ? 1 : 0) +
    (filters.priceRange.min > 0 || filters.priceRange.max < Math.max(...catalogueItems.map(item => item.price)) ? 1 : 0) +
    (filters.designFeeRange.min > 0 || filters.designFeeRange.max < Math.max(...catalogueItems.map(item => item.designFee || 0)) ? 1 : 0);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-orange-50/20">
      {/* Hero Section - Enhanced Design */}
      <div className="relative bg-gradient-to-br from-[#FF5400]/5 via-orange-50/50 to-red-50/30 border-b border-orange-100/50 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-[0.02]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="catalogue-grid" width="10" height="10" patternUnits="userSpaceOnUse">
                <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#FF5400" strokeWidth="0.5"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#catalogue-grid)" />
          </svg>
        </div>
        
        {/* Floating Elements */}
        <div className="absolute top-10 left-10 w-20 h-20 bg-orange-200/20 rounded-full blur-xl"></div>
        <div className="absolute top-32 right-20 w-32 h-32 bg-red-200/20 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 left-1/4 w-24 h-24 bg-orange-300/10 rounded-full blur-xl"></div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-20">
          <div className="text-center">
            {/* Enhanced Badge */}
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-orange-100 to-red-100 rounded-full px-6 py-3 mb-6 border border-orange-200/50 shadow-sm">
              <div className="bg-gradient-to-r from-[#FF5400] to-[#e84a00] rounded-full p-2">
                <SparklesIcon className="w-5 h-5 text-white" />
              </div>
              <span className="text-[#FF5400] font-semibold text-sm tracking-wide">OUR SERVICES CATALOGUE</span>
            </div>
            
            {/* Enhanced Title */}
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-black mb-6 text-gray-900 leading-tight">
              Discover{' '}
              <span className="bg-gradient-to-r from-[#FF5400] via-orange-500 to-red-500 bg-clip-text text-transparent">
                Professional
              </span>
              <br />Services & Solutions
            </h1>
            
            <p className="text-lg sm:text-xl text-gray-600 max-w-4xl mx-auto mb-10 leading-relaxed">
              Explore our comprehensive range of premium services and innovative solutions 
              designed to transform your business vision into reality
            </p>
            
            {/* Enhanced Search Bar */}
            <div className="max-w-3xl mx-auto relative group mb-8">
              <div className="absolute inset-0 bg-gradient-to-r from-orange-400/20 to-red-400/20 rounded-2xl blur-xl transition-all duration-300 group-hover:blur-2xl"></div>
              <div className="relative flex items-center bg-white/95 backdrop-blur-md rounded-2xl border border-white/20 shadow-2xl overflow-hidden">
                <div className="pl-6 pr-4 py-1">
                  <MagnifyingGlassIcon className="h-6 w-6 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search for services, categories, solutions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="flex-1 px-2 py-5 text-lg text-gray-900 placeholder-gray-500 bg-transparent border-0 focus:outline-none focus:ring-0"
                />
                {searchTerm && (
                  <button
                    onClick={() => setSearchTerm('')}
                    className="mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors rounded-full hover:bg-gray-100"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                )}
              </div>
            </div>
            
            {/* Quick Stats */}
            <div className="grid grid-cols-3 gap-8 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-3xl sm:text-4xl font-bold text-[#FF5400] mb-2">{catalogueItems.length}+</div>
                <div className="text-gray-600 text-sm font-medium">Services</div>
              </div>
              <div className="text-center">
                <div className="text-3xl sm:text-4xl font-bold text-[#FF5400] mb-2">{categories.length}+</div>
                <div className="text-gray-600 text-sm font-medium">Categories</div>
              </div>
              <div className="text-center">
                <div className="text-3xl sm:text-4xl font-bold text-[#FF5400] mb-2">24/7</div>
                <div className="text-gray-600 text-sm font-medium">Support</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Filters Section */}
      <section className="sticky top-0 z-40 bg-white/95 backdrop-blur-lg border-b border-gray-200/50 shadow-lg">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col lg:flex-row items-center justify-between gap-6">
            {/* Results Count */}
            <div className="flex items-center gap-4">
              <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg px-4 py-2 border border-gray-200">
                <span className="text-sm text-gray-600">Showing</span>
                <span className="font-bold text-[#FF5400] mx-2 text-lg">{filteredAndSortedItems.length}</span>
                <span className="text-sm text-gray-600">of {catalogueItems.length} services</span>
              </div>
              
              {activeFilterCount > 0 && (
                <button
                  onClick={clearFilters}
                  className="inline-flex items-center gap-2 bg-red-50 text-red-600 hover:bg-red-100 px-4 py-2 rounded-lg text-sm font-medium transition-colors border border-red-200"
                >
                  <XMarkIcon className="h-4 w-4" />
                  Clear {activeFilterCount} filter{activeFilterCount > 1 ? 's' : ''}
                </button>
              )}
            </div>

            {/* Enhanced Filter Controls */}
            <div className="flex items-center gap-4 flex-wrap">
              {/* Categories Dropdown */}
              <div className="relative">
                <select
                  value=""
                  onChange={(e) => e.target.value && toggleCategory(e.target.value)}
                  className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-10 text-sm font-medium text-gray-700 hover:border-[#FF5400] focus:ring-2 focus:ring-[#FF5400] focus:border-[#FF5400] transition-colors"
                >
                  <option value="">Add Category Filter</option>
                  {categories.filter(cat => !filters.categories.includes(cat)).map((category) => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
                <ChevronDownIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
              </div>

              {/* Sort Dropdown */}
              <div className="relative">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as any)}
                  className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-10 text-sm font-medium text-gray-700 hover:border-[#FF5400] focus:ring-2 focus:ring-[#FF5400] focus:border-[#FF5400] transition-colors"
                >
                  <option value="name">Sort by Name</option>
                  <option value="price-low">Price: Low to High</option>
                  <option value="price-high">Price: High to Low</option>
                  <option value="newest">Newest First</option>
                  <option value="popular">Most Popular</option>
                </select>
                <ChevronDownIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
              </div>

              {/* Advanced Filters Toggle */}
              <button className="inline-flex items-center gap-2 bg-gray-50 hover:bg-gray-100 border border-gray-300 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 transition-colors">
                <AdjustmentsHorizontalIcon className="h-4 w-4" />
                Filters
              </button>
            </div>
          </div>

          {/* Active Filters Display */}
          {filters.categories.length > 0 && (
            <div className="mt-4 flex items-center gap-2 flex-wrap">
              <span className="text-sm font-medium text-gray-700">Active filters:</span>
              {filters.categories.map((category) => (
                <span
                  key={category}
                  className="inline-flex items-center gap-1 bg-[#FF5400] text-white px-3 py-1 rounded-full text-sm font-medium"
                >
                  {category}
                  <button
                    onClick={() => toggleCategory(category)}
                    className="ml-1 hover:bg-white/20 rounded-full p-0.5 transition-colors"
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </button>
                </span>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Main Content Area */}
      <section className="py-16 bg-gradient-to-b from-white to-gray-50/50">
        <div className="container mx-auto px-4">
          {/* Loading State */}
          {loading && (
            <div className="flex flex-col justify-center items-center py-20">
              <div className="relative">
                <div className="animate-spin rounded-full h-16 w-16 border-4 border-gray-200 border-t-[#FF5400]"></div>
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-[#FF5400]/20 to-red-400/20 blur-lg"></div>
              </div>
              <div className="mt-6 text-center">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Services</h3>
                <p className="text-gray-600">Discovering amazing solutions for you...</p>
              </div>
            </div>
          )}

          {/* Services Grid */}
          {!loading && (
            <>
              {filteredAndSortedItems.length > 0 ? (
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
                  {filteredAndSortedItems.map((item, index) => (
                    <div
                      key={item.id}
                      className="transform hover:scale-[1.02] transition-all duration-300"
                      style={{ 
                        animationDelay: `${index * 100}ms`,
                        opacity: 0,
                        animation: 'fadeInUp 0.6s ease-out forwards'
                      }}
                    >
                      <SimplePricingCard
                        id={item.id}
                        service={item.service}
                        price={item.price}
                        designFee={item.designFee}
                        description={item.description}
                        features={item.features}
                        icon={item.icon}
                        popular={item.popular}
                        imageUrl={item.imageUrl}
                        imageUrl2={item.imageUrl2}
                        imageUrl3={item.imageUrl3}
                        category={item.category}
                        pricingType={item.pricingType}
                        unitType={item.unitType}
                        unitId={item.unitId}
                        unit={item.unit}
                        pricePerMeter={item.pricePerMeter}
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-20">
                  <div className="max-w-md mx-auto">
                    <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-full p-8 w-32 h-32 mx-auto mb-8 flex items-center justify-center">
                      <MagnifyingGlassIcon className="h-16 w-16 text-gray-400" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">No services found</h3>
                    <p className="text-gray-600 mb-8 leading-relaxed">
                      We couldn't find any services matching your criteria. Try adjusting your search or filters to discover more options.
                    </p>
                    <div className="space-y-4">
                      <button
                        onClick={clearFilters}
                        className="bg-[#FF5400] hover:bg-[#e84a00] text-white px-8 py-3 rounded-lg font-semibold transition-colors shadow-lg hover:shadow-xl"
                      >
                        Clear All Filters
                      </button>
                      <p className="text-sm text-gray-500">or try searching for something else</p>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </section>

      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        @media (prefers-reduced-motion: reduce) {
          * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
          }
        }
      `}</style>
    </div>
  );
}
