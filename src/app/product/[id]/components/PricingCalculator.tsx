import { ClockIcon, TruckIcon, MapPinIcon } from '@heroicons/react/24/outline';
import { 
  getUnitDisplay, 
  getProductionCost, 
  getDesignFee,
  type PricingItem 
} from '@/utils/pricing';

interface PricingCalculatorProps {
  product: PricingItem | null;
  quantity: string;
  meters: string;
  orderType: 'design' | 'print' | 'both';
  onQuantityChange: (value: string) => void;
  onMetersChange: (value: string) => void;
  onOrderTypeChange: (type: 'design' | 'print' | 'both') => void;
  onGetQuote: () => void;
  loading?: boolean;
}

/**
 * Interactive pricing calculator with order type selection
 * Extracted from main product page for better modularity
 */
export default function PricingCalculator({
  product,
  quantity,
  meters,
  orderType,
  onQuantityChange,
  onMetersChange,
  onOrderTypeChange,
  onGetQuote,
  loading = false
}: PricingCalculatorProps) {
  if (loading || !product) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6 space-y-4">
        <div className="h-6 bg-gray-200 rounded animate-pulse" />
        <div className="space-y-3">
          <div className="h-4 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 bg-gray-200 rounded animate-pulse" />
        </div>
        <div className="h-12 bg-gray-200 rounded animate-pulse" />
      </div>
    );
  }

  const actualQuantity = parseInt(quantity) || 1;
  const actualMeters = parseFloat(meters) || 1;
  const isMetreBased = product.pricingType === 'per_meter';
  const unitDisplay = getUnitDisplay(product);

  // Calculate pricing based on order type
  const designFee = (orderType === 'design' || orderType === 'both') ? getDesignFee(product) : 0;
  const printCost = (orderType === 'print' || orderType === 'both') ? getProductionCost(product, quantity, meters) : 0;
  const totalAmount = designFee + printCost;

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 space-y-6 sticky top-24">
      {/* Service Type Selection */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Select Service Type</h3>
        <div className="grid grid-cols-1 gap-3">
          <button
            onClick={() => onOrderTypeChange('design')}
            className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
              orderType === 'design'
                ? 'border-orange-500 bg-orange-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="font-medium text-gray-900">Design Only</div>
            <div className="text-sm text-gray-600">Get a custom design (no printing)</div>
            <div className="text-sm font-medium text-orange-600 mt-1">
              KSh {getDesignFee(product).toLocaleString()}
            </div>
          </button>

          <button
            onClick={() => onOrderTypeChange('print')}
            className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
              orderType === 'print'
                ? 'border-orange-500 bg-orange-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="font-medium text-gray-900">Print Only</div>
            <div className="text-sm text-gray-600">Bring your own design to print</div>
            <div className="text-sm font-medium text-orange-600 mt-1">
              {isMetreBased 
                ? `KSh ${(product.pricePerMeter || 0).toLocaleString()}/${unitDisplay.singular}`
                : `KSh ${(product.price || 0).toLocaleString()}/${unitDisplay.singular}`
              }
            </div>
          </button>

          <button
            onClick={() => onOrderTypeChange('both')}
            className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
              orderType === 'both'
                ? 'border-orange-500 bg-orange-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="font-medium text-gray-900">Design + Print</div>
            <div className="text-sm text-gray-600">Full service: design and printing</div>
            <div className="text-sm font-medium text-orange-600 mt-1">
              Complete package
            </div>
          </button>
        </div>
      </div>

      {/* Quantity/Size Input (only for print orders) */}
      {(orderType === 'print' || orderType === 'both') && (
        <div className="space-y-3">
          <label className="block text-sm font-medium text-gray-700">
            {isMetreBased ? `Size (${unitDisplay.plural})` : `Quantity (${unitDisplay.plural})`}
          </label>
          {isMetreBased ? (
            <input
              type="number"
              min="0.1"
              step="0.1"
              value={meters}
              onChange={(e) => onMetersChange(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder={`Enter ${unitDisplay.plural}`}
            />
          ) : (
            <input
              type="number"
              min="1"
              value={quantity}
              onChange={(e) => onQuantityChange(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder={`Enter number of ${unitDisplay.plural}`}
            />
          )}
        </div>
      )}

      {/* Price Breakdown */}
      <div className="space-y-3 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium text-gray-900">Price Breakdown</h4>
        
        {(orderType === 'design' || orderType === 'both') && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Design Fee (one-time)</span>
            <span className="font-medium">KSh {designFee.toLocaleString()}</span>
          </div>
        )}
        
        {(orderType === 'print' || orderType === 'both') && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">
              Production Cost ({isMetreBased ? actualMeters : actualQuantity} {unitDisplay.plural})
            </span>
            <span className="font-medium">KSh {printCost.toLocaleString()}</span>
          </div>
        )}
        
        <div className="border-t border-gray-200 pt-2">
          <div className="flex justify-between">
            <span className="font-semibold text-gray-900">Total</span>
            <span className="font-bold text-xl text-orange-600">
              KSh {totalAmount.toLocaleString()}
            </span>
          </div>
        </div>
      </div>

      {/* Service Info */}
      <div className="space-y-3 text-sm text-gray-600">
        <div className="flex items-center space-x-2">
          <ClockIcon className="w-4 h-4" />
          <span>2-5 business days turnaround</span>
        </div>
        <div className="flex items-center space-x-2">
          <TruckIcon className="w-4 h-4" />
          <span>Free delivery within Nairobi</span>
        </div>
        <div className="flex items-center space-x-2">
          <MapPinIcon className="w-4 h-4" />
          <span>Pickup available at our studio</span>
        </div>
      </div>

      {/* Get Quote Button */}
      <button
        onClick={onGetQuote}
        className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold py-4 px-6 rounded-lg transition-all duration-200 transform hover:-translate-y-0.5 shadow-lg hover:shadow-xl"
      >
        Get Quote & Place Order
      </button>
    </div>
  );
} 