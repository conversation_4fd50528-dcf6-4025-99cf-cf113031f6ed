import { ShareIcon } from '@heroicons/react/24/outline';

interface Product {
  service: string;
  description?: string;  // Make description optional
  category?: string;
  tags?: string[];
}

interface ProductInfoProps {
  product: Product;
  onShare: () => void;
  loading?: boolean;
}

/**
 * Product information display component
 * Shows title, description, category and share functionality
 */
export default function ProductInfo({ product, onShare, loading = false }: ProductInfoProps) {
  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-start">
          <div className="flex-1 space-y-3">
            <div className="h-8 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 bg-gray-200 rounded w-24 animate-pulse" />
          </div>
          <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse" />
        </div>
        <div className="space-y-2">
          <div className="h-4 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Title and Share */}
      <div className="flex justify-between items-start gap-4">
        <div className="flex-1">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {product.service}
          </h1>
          {product.category && (
            <span className="inline-block px-3 py-1 bg-orange-100 text-orange-800 text-sm font-medium rounded-full">
              {product.category}
            </span>
          )}
        </div>
        
        <button
          onClick={onShare}
          className="p-3 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors duration-200 group"
          aria-label="Share product"
        >
          <ShareIcon className="w-5 h-5 text-gray-600 group-hover:text-gray-800" />
        </button>
      </div>

      {/* Description */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-900">About This Service</h2>
        <div className="prose prose-gray max-w-none">
          <p className="text-gray-600 leading-relaxed">
            {product.description}
          </p>
        </div>
      </div>

      {/* Tags */}
      {product.tags && product.tags.length > 0 && (
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wider">
            Tags
          </h3>
          <div className="flex flex-wrap gap-2">
            {product.tags.map((tag, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200 transition-colors"
              >
                {tag}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 