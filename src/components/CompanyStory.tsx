'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';

// Hardcoded company story data
const companyStory = {
  title: 'Our Journey',
  subtitle: 'From a vision to transform Kenya\'s digital landscape to becoming a trusted partner for businesses across East Africa. We specialize in delivering exceptional design, branding, and digital marketing solutions that help local businesses compete globally while staying true to their roots. Our journey is built on innovation, creativity, and an unwavering commitment to empowering African entrepreneurs with world-class digital experiences.',
  imageSrc: '/images/about/ceo.jpg',
  quote1: 'We started Mocky Digital with a simple mission: to help businesses succeed in the digital world through exceptional design and marketing. Our team combines creative expertise with strategic thinking to deliver custom solutions that elevate brands and drive growth in today\'s competitive marketplace.',
  quote2: 'Every day, we strive to deliver work that not only looks great but also drives real business results for our clients. Whether it\'s logo design, web development, or digital marketing campaigns, we approach each project with the same dedication to quality and measurable outcomes.',
  founder<PERSON><PERSON>: '<PERSON>',
  founderR<PERSON>: 'Creative Director',
  linkedinUrl: 'https://www.linkedin.com/in/don-omondi-*********/',
  twitterUrl: 'https://x.com/onyango__omondi',
  instagramUrl: 'https://www.instagram.com/tk_omondi/',
  tiktokUrl: 'https://www.tiktok.com/@mocky_digital'
};

export default function CompanyStory() {
  return (
    <section className="py-16 md:py-24 relative overflow-hidden bg-gray-50">
      {/* Modern background elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Geometric background pattern */}
        <div className="absolute top-0 left-0 w-full h-full opacity-[0.02]">
          <div className="absolute top-20 left-10 w-32 h-32 border border-[#0A2647] rotate-45"></div>
          <div className="absolute top-40 right-20 w-24 h-24 border border-[#FF5400] rotate-12"></div>
          <div className="absolute bottom-32 left-1/4 w-16 h-16 border border-[#0A2647] rotate-45"></div>
        </div>

        {/* Gradient overlays */}
        <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-gradient-to-bl from-[#FF5400]/5 to-transparent"></div>
        <div className="absolute bottom-0 left-0 w-1/3 h-1/3 bg-gradient-to-tr from-[#0A2647]/5 to-transparent"></div>
      </div>

      <div className="container relative z-10 mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Enhanced header section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16 md:mb-20"
          >
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white border border-[#0A2647]/10 shadow-sm mb-6">
              <div className="w-2 h-2 rounded-full bg-[#FF5400]"></div>
              <span className="text-[#0A2647] text-sm font-medium">Our Story</span>
            </div>

            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-[#0A2647] mb-6 leading-tight">
              {companyStory.title}
            </h2>

            <div className="w-24 h-1 bg-gradient-to-r from-[#FF5400] to-[#FF5400]/60 mx-auto mb-8"></div>

            <p className="text-[#0A2647]/70 text-lg md:text-xl max-w-4xl mx-auto leading-relaxed">
              {companyStory.subtitle}
            </p>
          </motion.div>

          {/* Modern content layout */}
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            {/* Founder image section */}
            <motion.div
              initial={{ opacity: 0, x: -40 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative order-2 lg:order-1"
            >
              <div className="relative group">
                {/* Modern image container */}
                <div className="relative rounded-2xl overflow-hidden shadow-2xl bg-white p-2">
                  <div className="relative rounded-xl overflow-hidden">
                    <Image
                      src={companyStory.imageSrc}
                      alt={`${companyStory.founderName} of Mocky Digital`}
                      width={600}
                      height={700}
                      className="object-cover w-full aspect-[4/5] transition-transform duration-500 group-hover:scale-105"
                      style={{ objectPosition: "center 10%" }}
                      priority
                    />

                    {/* Subtle gradient overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-[#0A2647]/80 via-transparent to-transparent"></div>
                  </div>
                </div>

                {/* Floating founder info card */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.3 }}
                  viewport={{ once: true }}
                  className="absolute -bottom-6 -right-6 bg-white rounded-xl shadow-xl p-6 border border-gray-100"
                >
                  <h3 className="text-xl font-bold text-[#0A2647] mb-1">
                    {companyStory.founderName}
                  </h3>
                  <p className="text-[#0A2647]/70 text-sm font-medium mb-4">
                    {companyStory.founderRole}
                  </p>

                  {/* Modern social links */}
                  <div className="flex gap-2">
                    <a
                      href={companyStory.linkedinUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-8 h-8 rounded-lg bg-[#0A2647]/5 hover:bg-[#0A66C2] hover:text-white flex items-center justify-center text-[#0A2647] transition-all duration-300"
                      aria-label="LinkedIn Profile"
                    >
                      <i className="fab fa-linkedin-in text-sm"></i>
                    </a>
                    <a
                      href={companyStory.twitterUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-8 h-8 rounded-lg bg-[#0A2647]/5 hover:bg-[#1DA1F2] hover:text-white flex items-center justify-center text-[#0A2647] transition-all duration-300"
                      aria-label="Twitter Profile"
                    >
                      <i className="fab fa-twitter text-sm"></i>
                    </a>
                    <a
                      href={companyStory.instagramUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-8 h-8 rounded-lg bg-[#0A2647]/5 hover:bg-gradient-to-br hover:from-[#833AB4] hover:to-[#FCAF45] hover:text-white flex items-center justify-center text-[#0A2647] transition-all duration-300"
                      aria-label="Instagram Profile"
                    >
                      <i className="fab fa-instagram text-sm"></i>
                    </a>
                    <a
                      href={companyStory.tiktokUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-8 h-8 rounded-lg bg-[#0A2647]/5 hover:bg-black hover:text-white flex items-center justify-center text-[#0A2647] transition-all duration-300"
                      aria-label="TikTok Profile"
                    >
                      <i className="fab fa-tiktok text-sm"></i>
                    </a>
                  </div>
                </motion.div>

                {/* Decorative elements */}
                <div className="absolute -top-4 -left-4 w-8 h-8 border-2 border-[#FF5400] rounded-full opacity-60"></div>
                <div className="absolute top-1/4 -right-2 w-4 h-4 bg-[#FF5400] rounded-full opacity-40"></div>
              </div>
            </motion.div>

            {/* Enhanced quote section */}
            <motion.div
              initial={{ opacity: 0, x: 40 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="relative order-1 lg:order-2"
            >
              <div className="relative">
                {/* Main quote card */}
                <div className="relative bg-white rounded-2xl shadow-xl p-8 md:p-10 border border-gray-100">
                  {/* Quote icon */}
                  <div className="absolute -top-4 -left-4 w-12 h-12 bg-[#FF5400] rounded-full flex items-center justify-center shadow-lg">
                    <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
                    </svg>
                  </div>

                  {/* Quote content */}
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <p className="text-lg md:text-xl text-[#0A2647]/80 leading-relaxed font-medium">
                        {companyStory.quote1}
                      </p>
                      {companyStory.quote2 && (
                        <p className="text-lg md:text-xl text-[#0A2647]/80 leading-relaxed font-medium">
                          {companyStory.quote2}
                        </p>
                      )}
                    </div>

                    {/* Signature line */}
                    <div className="flex items-center gap-4 pt-6 border-t border-gray-100">
                      <div className="flex-1 h-px bg-gradient-to-r from-[#FF5400] to-transparent"></div>
                      <div className="text-sm font-medium text-[#0A2647]/60">
                        {companyStory.founderName}
                      </div>
                    </div>
                  </div>

                  {/* Year badge */}
                  <div className="absolute -bottom-4 -right-4 w-14 h-14 md:w-16 md:h-16 bg-[#0A2647] rounded-full flex items-center justify-center shadow-xl hidden sm:flex">
                    <div className="text-center">
                      <div className="text-white font-bold text-xs md:text-sm leading-none">
                        {new Date().getFullYear()}
                      </div>
                      <div className="text-[#FF5400] text-xs font-medium">EST</div>
                    </div>
                  </div>
                </div>

                {/* Background decorative elements */}
                <div className="absolute top-8 right-8 w-20 h-20 border border-[#FF5400]/20 rounded-full -z-10"></div>
                <div className="absolute bottom-8 left-8 w-12 h-12 bg-[#0A2647]/5 rounded-full -z-10"></div>
              </div>
            </motion.div>
          </div>

          {/* Additional stats or achievements section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="mt-16 md:mt-20"
          >
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {[
                { number: '50+', label: 'Projects Completed' },
                { number: '30+', label: 'Happy Clients' },
                { number: '3+', label: 'Years Experience' },
                { number: '24/7', label: 'Support Available' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-[#0A2647] mb-2">
                    {stat.number}
                  </div>
                  <div className="text-[#0A2647]/60 text-sm font-medium">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
